package test

import (
	"testing"
	"time"

	"tp.service/internal/common"
	"tp.service/internal/logger"
)

func TestSerial(t *testing.T) {
	serialPort := common.NewSerial(&common.SerialConfig{
		Port:     "COM113",
		BaudRate: 9600,
		DataBits: common.DataBits8,
		StopBits: common.StopBits1,
		Parity:   common.ParityNone,
		Timeout:  5000 * time.Millisecond,
	})
	logger.Info("串口参数: %v", serialPort)

	if err := serialPort.Connect(); err != nil {
		t.Error(err)
		return
	}
	logger.Info("串口连接成功")

	tmp := []byte{0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A}
	if err := serialPort.Send(tmp, 3); err != nil {
		t.Error(err)
		return
	}
	logger.Info("发送数据成功")

	recv, err := serialPort.Receive(0)
	if err != nil {
		t.Error(err)
		return
	}
	logger.Info("接收数据成功: %v, String: %s", recv, string(recv))

	if err := serialPort.Disconnect(); err != nil {
		t.Error(err)
		return
	}
	logger.Info("串口断开成功")
}
