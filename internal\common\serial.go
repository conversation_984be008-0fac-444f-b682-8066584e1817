package common

import (
	"fmt"
	"sync"
	"time"

	"go.bug.st/serial"
	"tp.service/internal/logger"
)

type DataBits int

type StopBits int

type Parity = serial.Parity

// SerialConfig 表示串口配置
type SerialConfig struct {
	Port     string        // 端口名称
	BaudRate int           // 波特率
	DataBits DataBits      // 数据位
	StopBits StopBits      // 停止位
	Parity   Parity        // 校验位
	Timeout  time.Duration // 超时时间(毫秒)
}

// SerialPort 表示串口
type serialPort struct {
	config    SerialConfig
	port      serial.Port
	mutex     sync.Mutex
	isOpen    bool
	isReceive bool
}

const (
	DataBits5 DataBits = 5
	DataBits6 DataBits = 6
	DataBits7 DataBits = 7
	DataBits8 DataBits = 8

	StopBits1  StopBits = 1
	StopBits2  StopBits = 2
	StopBits15 StopBits = 15

	ParityNone  Parity = serial.NoParity
	ParityEven  Parity = serial.EvenParity
	ParityOdd   Parity = serial.OddParity
	ParityMark  Parity = serial.MarkParity
	ParitySpace Parity = serial.SpaceParity
)

func NewSerial(config *SerialConfig) *serialPort {
	return &serialPort{
		config: *config,
		isOpen: false,
	}
}

func (s *serialPort) Connect() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	logger.Debug("连接串口: %v", s.config)

	if s.isOpen {
		logger.Debug("串口已连接")
		return nil
	}

	// 创建串口配置
	mode := &serial.Mode{
		BaudRate: s.config.BaudRate,
		DataBits: int(s.config.DataBits),
		Parity:   s.config.Parity,
		StopBits: serial.StopBits(s.config.StopBits),
	}

	// 打开串口
	port, err := serial.Open(s.config.Port, mode)
	if err != nil {
		return fmt.Errorf("打开串口失败: %w", err)
	}

	// 设置超时
	if s.config.Timeout > 0 {
		err = port.SetReadTimeout(s.config.Timeout)
		if err != nil {
			port.Close()
			return fmt.Errorf("设置超时失败: %w", err)
		}
	}

	s.port = port
	s.isOpen = true
	return nil
}

func (s *serialPort) Disconnect() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isOpen {
		return nil
	}

	err := s.port.Close()
	if err != nil {
		return fmt.Errorf("关闭串口失败: %w", err)
	}

	s.isOpen = false
	return nil
}

func (s *serialPort) IsConnected() bool {
	// 检查校验是否真的已连接
	if s.isOpen {
		// 检查串口是否已关闭
		if err := s.port.ResetInputBuffer(); err != nil {
			s.isOpen = false
			return false
		}
		return true
	}
	return false
}

func (s *serialPort) SetTimeout(timeout time.Duration) error {
	return nil
}

func (s *serialPort) GetTimeout() time.Duration {
	return 0
}

func (s *serialPort) Send(data []byte, retry int) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	logger.Debug("发送数据: %v", data)

	if !s.isOpen {
		return fmt.Errorf("串口未打开")
	}
	if retry <= 0 {
		retry = 1
	}
	for i := 0; i < retry; i++ {
		senLen, err := s.port.Write(data)
		if err != nil {
			logger.Error("发送数据失败: %v", err)
			continue
		}
		if senLen != len(data) {
			logger.Error("发送数据失败: 发送长度不匹配")
			continue
		}
		logger.Debug("发送数据成功: %v", data)
		return nil
	}
	return fmt.Errorf("发送数据失败: 重试次数耗尽")
}

func (s *serialPort) Receive(timeout time.Duration) ([]byte, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	logger.Debug("接收数据")
	if !s.isOpen {
		return nil, fmt.Errorf("串口未打开")
	}

	tmpTimeout := s.config.Timeout
	if timeout > 0 {
		tmpTimeout = timeout
	}

	err := s.port.SetReadTimeout(tmpTimeout)
	if err != nil {
		return nil, fmt.Errorf("设置超时失败: %w", err)
	}

	logger.Debug("开始读取数据")
	recv := make([]byte, 1024)
	n, err := s.port.Read(recv)
	if err != nil {
		return nil, fmt.Errorf("读取数据失败: %w", err)
	}
	if n == 0 {
		return nil, fmt.Errorf("读取数据失败: 无数据")
	}
	recv = recv[:n]
	logger.Debug("接收数据成功: %v", recv)

	return recv, nil
}

func (s *serialPort) ReceiveStream(channel chan []byte) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	if !s.isOpen {
		return fmt.Errorf("串口未打开")
	}

	s.isReceive = true
	for s.isReceive {
		recv, err := s.Receive(0)
		if err != nil {
			return err
		}
		channel <- recv
	}

	return nil

}

func (s *serialPort) StopReceive() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	if !s.isOpen {
		return fmt.Errorf("串口未打开")
	}
	s.isReceive = false
	return nil
}

// func (s *serialPort) ReceiveStream() ([]byte, error) {
// 	s.mutex.Lock()
// 	defer s.mutex.Unlock()
// 	if !s.isOpen {
// 		return nil, fmt.Errorf("串口未打开")
// 	}

// 	s.isReceive = true
// 	for s.isReceive {
// 		recv, err := s.Receive(0)
// 		if err != nil {
// 			return nil, err
// 		}
// 		logger.Debug("接收数据成功: %v", recv)
// 	}

// 	// 接收数据
// 	return nil, nil
// }

// func (s *serialPort) ReceiveSop() error {
// 	s.mutex.Lock()
// 	defer s.mutex.Unlock()
// 	if !s.isOpen {
// 		return fmt.Errorf("串口未打开")
// 	}
// 	return nil
// }

func (s *serialPort) SendAndReceive(data []byte) ([]byte, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	if !s.isOpen {
		return nil, fmt.Errorf("串口未打开")
	}
	logger.Debug("发送并接收数据: %v", data)
	if err := s.Send(data, 3); err != nil {
		return nil, err
	}
	recv, err := s.Receive(0)
	if err != nil {
		return nil, err
	}
	logger.Debug("发送并接收数据成功: %v", recv)
	return nil, nil
}
