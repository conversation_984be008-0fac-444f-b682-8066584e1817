package dlt698

// 基本数据类型定义
type (
	Unsigned           uint8
	LongUnsigned       uint16
	DoubleLongUnsigned uint32
	Long64Unsigned     uint64
	Integer            int8
	Long               int16
	DoubleLong         int32
	Long64             int64
	Enum               uint8
	Bool               bool
	BitString          []byte
	OctetString        []byte
	VisibleString      string
	UTF8String         string
	Float32            float32
	Float64            float64
)

// PIID 服务序号-优先级
type PIID uint8

// PIID_ACD 服务序号-优先级-ACD
type PIID_ACD uint8

// OI 对象标识
type OI uint16

// OAD 对象属性描述符
type OAD struct {
	OI             OI
	AttributeID    Unsigned
	AttributeIndex Unsigned
}

// DAR 数据访问结果
type DAR uint8

// DAR 枚举值定义
const (
	DAR_SUCCESS                   DAR = 0   // 成功
	DAR_HARDWARE_FAILURE          DAR = 1   // 硬件失效
	DAR_TEMPORARY_FAILURE         DAR = 2   // 暂时失效
	DAR_READ_WRITE_DENIED         DAR = 3   // 拒绝读写
	DAR_OBJECT_UNDEFINED          DAR = 4   // 对象未定义
	DAR_OBJECT_INTERFACE_MISMATCH DAR = 5   // 对象接口类不符合
	DAR_OBJECT_NOT_EXIST          DAR = 6   // 对象不存在
	DAR_TYPE_MISMATCH             DAR = 7   // 类型不匹配
	DAR_OUT_OF_RANGE              DAR = 8   // 越界
	DAR_DATA_BLOCK_UNAVAILABLE    DAR = 9   // 数据块不可用
	DAR_FRAME_TRANSFER_CANCELED   DAR = 10  // 分帧传输已取消
	DAR_NOT_IN_FRAME_TRANSFER     DAR = 11  // 不处于分帧传输状态
	DAR_BLOCK_WRITE_CANCELED      DAR = 12  // 块写取消
	DAR_NO_BLOCK_WRITE_STATUS     DAR = 13  // 不存在块写状态
	DAR_INVALID_BLOCK_NUMBER      DAR = 14  // 数据块序号无效
	DAR_PASSWORD_ERROR            DAR = 15  // 密码错/未授权
	DAR_COMM_RATE_UNCHANGEABLE    DAR = 16  // 通信速率不能更改
	DAR_YEAR_ZONE_EXCEEDED        DAR = 17  // 年时区数超
	DAR_DAY_PERIOD_EXCEEDED       DAR = 18  // 日时段数超
	DAR_RATE_EXCEEDED             DAR = 19  // 费率数超
	DAR_SECURITY_MISMATCH         DAR = 20  // 安全认证不匹配
	DAR_DUPLICATE_RECHARGE        DAR = 21  // 重复充值
	DAR_ESAM_VERIFY_FAILED        DAR = 22  // ESAM 验证失败
	DAR_SECURITY_AUTH_FAILED      DAR = 23  // 安全认证失败
	DAR_CUSTOMER_NUMBER_MISMATCH  DAR = 24  // 客户编号不匹配
	DAR_RECHARGE_COUNT_ERROR      DAR = 25  // 充值次数错误
	DAR_PURCHASE_EXCESS           DAR = 26  // 购电超囤积
	DAR_ADDRESS_ABNORMAL          DAR = 27  // 地址异常
	DAR_SYMMETRIC_DECRYPT_ERROR   DAR = 28  // 对称解密错误
	DAR_ASYMMETRIC_DECRYPT_ERROR  DAR = 29  // 非对称解密错误
	DAR_SIGNATURE_ERROR           DAR = 30  // 签名错误
	DAR_METER_SUSPENDED           DAR = 31  // 电能表挂起
	DAR_INVALID_TIME_TAG          DAR = 32  // 时间标签无效
	DAR_REQUEST_TIMEOUT           DAR = 33  // 请求超时
	DAR_ESAM_P1P2_INCORRECT       DAR = 34  // ESAM 的 P1P2 不正确
	DAR_ESAM_LC_ERROR             DAR = 35  // ESAM 的 LC 错误
	DAR_COMPARISON_FAILED         DAR = 36  // 比对失败
	DAR_OTHER                     DAR = 255 // 其它
)

// ROAD 记录型对象属性描述符
type ROAD struct {
	OAD        OAD
	RelatedOAD []OAD
}

// OMD 对象方法描述符
type OMD struct {
	OI            OI
	MethodID      Unsigned
	OperationMode Unsigned
}

// TI 时间间隔
type TI struct {
	Unit     Unsigned // 0:秒, 1:分, 2:时, 3:日, 4:月, 5:年
	Interval LongUnsigned
}

// TSA 终端地址
type TSA OctetString

// MAC 消息认证码
type MAC OctetString

// RN 随机数
type RN OctetString

// Region 区间
type Region struct {
	Unit       Unsigned // 0:前闭后开, 1:前开后闭, 2:前闭后闭, 3:前开后开
	StartValue interface{}
	EndValue   interface{}
}

// ScalerUnit 换算单位
type ScalerUnit struct {
	Scaler Integer
	Unit   Enum
}

// RSD 记录选择描述符
type RSD interface{}

// CSD 列选择描述符
type CSD interface {
	IsCSD()
}

// CSD_OAD 对象属性描述符类型的CSD
type CSD_OAD struct {
	OAD OAD
}

func (CSD_OAD) IsCSD() {}

// CSD_ROAD 记录型对象属性描述符类型的CSD
type CSD_ROAD struct {
	ROAD ROAD
}

func (CSD_ROAD) IsCSD() {}

// MS 电表集合
type MS interface{}

// SID 安全标识
type SID struct {
	ID        DoubleLongUnsigned
	ExtraData OctetString
}

// SID_MAC 带MAC的安全标识
type SID_MAC struct {
	SID SID
	MAC MAC
}

// COMDCB 通信参数
type COMDCB struct {
	BaudRate    Enum // 0:300bps, 1:600bps, 2:1200bps, 3:2400bps, 4:4800bps, 5:7200bps, 6:9600bps, 7:19200bps, 8:38400bps, 9:57600bps, 10:115200bps, 11:230400bps, 12:460800bps, 255:自适应
	Parity      Enum // 0:无校验, 1:奇校验, 2:偶校验
	DataBits    Enum // 5, 6, 7, 8
	StopBits    Enum // 1, 2
	FlowControl Enum // 0:无, 1:硬件, 2:软件
}

// RCSD 记录列选择描述符
type RCSD []CSD

// VQDS 带品质描述的值
type VQDS struct {
	Value interface{}
	QDS   BitString
}

// DateTime 日期时间
type DateTime struct {
	Year         LongUnsigned
	Month        Unsigned
	DayOfMonth   Unsigned
	DayOfWeek    Unsigned
	Hour         Unsigned
	Minute       Unsigned
	Second       Unsigned
	Milliseconds LongUnsigned
}

// DateTimeS 简化日期时间
type DateTimeS struct {
	Year   LongUnsigned
	Month  Unsigned
	Day    Unsigned
	Hour   Unsigned
	Minute Unsigned
	Second Unsigned
}

// Date 日期
type Date struct {
	Year       LongUnsigned
	Month      Unsigned
	DayOfMonth Unsigned
	DayOfWeek  Unsigned
}

// Time 时间
type Time struct {
	Hour   Unsigned
	Minute Unsigned
	Second Unsigned
}

// TimeTag 时间标签
type TimeTag struct {
	DateTime DateTime // 使用DateTime类型表示时间标签
}
