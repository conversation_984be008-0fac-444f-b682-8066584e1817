// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/detection/detection.proto

package detection

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务信息
type ServiceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`               // 服务名称
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`         // 服务版本
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 服务描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceInfo) Reset() {
	*x = ServiceInfo{}
	mi := &file_api_detection_detection_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfo) ProtoMessage() {}

func (x *ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfo.ProtoReflect.Descriptor instead.
func (*ServiceInfo) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ServiceInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 检测项
type DetectionItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                  // 检测项ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`               // 检测项名称
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 检测项描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionItem) Reset() {
	*x = DetectionItem{}
	mi := &file_api_detection_detection_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionItem) ProtoMessage() {}

func (x *DetectionItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionItem.ProtoReflect.Descriptor instead.
func (*DetectionItem) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{1}
}

func (x *DetectionItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DetectionItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetectionItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 服务信息获取请求
type GetServiceInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 检测服务名称，如果为空("")则获取所有服务信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoRequest) Reset() {
	*x = GetServiceInfoRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoRequest) ProtoMessage() {}

func (x *GetServiceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{2}
}

func (x *GetServiceInfoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 服务信息获取响应
type GetServiceInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []*ServiceInfo         `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoResponse) Reset() {
	*x = GetServiceInfoResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoResponse) ProtoMessage() {}

func (x *GetServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{3}
}

func (x *GetServiceInfoResponse) GetServices() []*ServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

// 检测初始化请求
type DetectionInitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`     // 检测服务名称
	Config        string                 `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"` // 检测配置, JSON 格式（待定）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionInitRequest) Reset() {
	*x = DetectionInitRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionInitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionInitRequest) ProtoMessage() {}

func (x *DetectionInitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionInitRequest.ProtoReflect.Descriptor instead.
func (*DetectionInitRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{4}
}

func (x *DetectionInitRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetectionInitRequest) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

// 检测初始化响应
type DetectionInitResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // 检测令牌，用于后续访问检测服务
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionInitResponse) Reset() {
	*x = DetectionInitResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionInitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionInitResponse) ProtoMessage() {}

func (x *DetectionInitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionInitResponse.ProtoReflect.Descriptor instead.
func (*DetectionInitResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{5}
}

func (x *DetectionInitResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测清理请求
type DetectionCleanupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // 检测令牌
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionCleanupRequest) Reset() {
	*x = DetectionCleanupRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionCleanupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionCleanupRequest) ProtoMessage() {}

func (x *DetectionCleanupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionCleanupRequest.ProtoReflect.Descriptor instead.
func (*DetectionCleanupRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{6}
}

func (x *DetectionCleanupRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测清理响应
type DetectionCleanupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionCleanupResponse) Reset() {
	*x = DetectionCleanupResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionCleanupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionCleanupResponse) ProtoMessage() {}

func (x *DetectionCleanupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionCleanupResponse.ProtoReflect.Descriptor instead.
func (*DetectionCleanupResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{7}
}

// 检测项获取请求
type DetectionGetItemsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // 检测令牌
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionGetItemsRequest) Reset() {
	*x = DetectionGetItemsRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionGetItemsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionGetItemsRequest) ProtoMessage() {}

func (x *DetectionGetItemsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionGetItemsRequest.ProtoReflect.Descriptor instead.
func (*DetectionGetItemsRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{8}
}

func (x *DetectionGetItemsRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测项获取响应
type DetectionGetItemsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*DetectionItem       `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionGetItemsResponse) Reset() {
	*x = DetectionGetItemsResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionGetItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionGetItemsResponse) ProtoMessage() {}

func (x *DetectionGetItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionGetItemsResponse.ProtoReflect.Descriptor instead.
func (*DetectionGetItemsResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{9}
}

func (x *DetectionGetItemsResponse) GetItems() []*DetectionItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// 检测启动请求
type DetectionStartRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`                            // 检测令牌
	ItemIds       []int32                `protobuf:"varint,2,rep,packed,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"` // 检测项 ID 集, 如果为空则启动所有检测项
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStartRequest) Reset() {
	*x = DetectionStartRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStartRequest) ProtoMessage() {}

func (x *DetectionStartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStartRequest.ProtoReflect.Descriptor instead.
func (*DetectionStartRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{10}
}

func (x *DetectionStartRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DetectionStartRequest) GetItemIds() []int32 {
	if x != nil {
		return x.ItemIds
	}
	return nil
}

// 检测启动响应（服务端流式响应）
type DetectionStartStreamResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemId        int32                  `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                // 检测项 ID
	MessageType   int32                  `protobuf:"varint,2,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"` // 消息类型 -> 0: 检测项开始, 1: 检测项进度, 3: 检测项过程消息, 4: 检测项成功消息, 4: 检测项失败消息
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`                             // 消息内容
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStartStreamResponse) Reset() {
	*x = DetectionStartStreamResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStartStreamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStartStreamResponse) ProtoMessage() {}

func (x *DetectionStartStreamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStartStreamResponse.ProtoReflect.Descriptor instead.
func (*DetectionStartStreamResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{11}
}

func (x *DetectionStartStreamResponse) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *DetectionStartStreamResponse) GetMessageType() int32 {
	if x != nil {
		return x.MessageType
	}
	return 0
}

func (x *DetectionStartStreamResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 检测停止请求
type DetectionStopRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // 检测令牌
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStopRequest) Reset() {
	*x = DetectionStopRequest{}
	mi := &file_api_detection_detection_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStopRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStopRequest) ProtoMessage() {}

func (x *DetectionStopRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStopRequest.ProtoReflect.Descriptor instead.
func (*DetectionStopRequest) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{12}
}

func (x *DetectionStopRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 检测停止响应
type DetectionStopResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectionStopResponse) Reset() {
	*x = DetectionStopResponse{}
	mi := &file_api_detection_detection_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectionStopResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectionStopResponse) ProtoMessage() {}

func (x *DetectionStopResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_detection_detection_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectionStopResponse.ProtoReflect.Descriptor instead.
func (*DetectionStopResponse) Descriptor() ([]byte, []int) {
	return file_api_detection_detection_proto_rawDescGZIP(), []int{13}
}

var File_api_detection_detection_proto protoreflect.FileDescriptor

const file_api_detection_detection_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/detection/detection.proto\x12\tdetection\"]\n" +
	"\vServiceInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"U\n" +
	"\rDetectionItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"+\n" +
	"\x15GetServiceInfoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"L\n" +
	"\x16GetServiceInfoResponse\x122\n" +
	"\bservices\x18\x01 \x03(\v2\x16.detection.ServiceInfoR\bservices\"B\n" +
	"\x14DetectionInitRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06config\x18\x02 \x01(\tR\x06config\"-\n" +
	"\x15DetectionInitResponse\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"/\n" +
	"\x17DetectionCleanupRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\x1a\n" +
	"\x18DetectionCleanupResponse\"0\n" +
	"\x18DetectionGetItemsRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"K\n" +
	"\x19DetectionGetItemsResponse\x12.\n" +
	"\x05items\x18\x01 \x03(\v2\x18.detection.DetectionItemR\x05items\"H\n" +
	"\x15DetectionStartRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x19\n" +
	"\bitem_ids\x18\x02 \x03(\x05R\aitemIds\"t\n" +
	"\x1cDetectionStartStreamResponse\x12\x17\n" +
	"\aitem_id\x18\x01 \x01(\x05R\x06itemId\x12!\n" +
	"\fmessage_type\x18\x02 \x01(\x05R\vmessageType\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\",\n" +
	"\x14DetectionStopRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\x17\n" +
	"\x15DetectionStopResponse2\xb7\x04\n" +
	"\x14BaseDetectionService\x12U\n" +
	"\x0eGetServiceInfo\x12 .detection.GetServiceInfoRequest\x1a!.detection.GetServiceInfoResponse\x12X\n" +
	"\x13DetectionInitialize\x12\x1f.detection.DetectionInitRequest\x1a .detection.DetectionInitResponse\x12[\n" +
	"\x10DetectionCleanup\x12\".detection.DetectionCleanupRequest\x1a#.detection.DetectionCleanupResponse\x12^\n" +
	"\x11DetectionGetItems\x12#.detection.DetectionGetItemsRequest\x1a$.detection.DetectionGetItemsResponse\x12]\n" +
	"\x0eDetectionStart\x12 .detection.DetectionStartRequest\x1a'.detection.DetectionStartStreamResponse0\x01\x12R\n" +
	"\rDetectionStop\x12\x1f.detection.DetectionStopRequest\x1a .detection.DetectionStopResponseB\rZ\v./detectionb\x06proto3"

var (
	file_api_detection_detection_proto_rawDescOnce sync.Once
	file_api_detection_detection_proto_rawDescData []byte
)

func file_api_detection_detection_proto_rawDescGZIP() []byte {
	file_api_detection_detection_proto_rawDescOnce.Do(func() {
		file_api_detection_detection_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_detection_detection_proto_rawDesc), len(file_api_detection_detection_proto_rawDesc)))
	})
	return file_api_detection_detection_proto_rawDescData
}

var file_api_detection_detection_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_detection_detection_proto_goTypes = []any{
	(*ServiceInfo)(nil),                  // 0: detection.ServiceInfo
	(*DetectionItem)(nil),                // 1: detection.DetectionItem
	(*GetServiceInfoRequest)(nil),        // 2: detection.GetServiceInfoRequest
	(*GetServiceInfoResponse)(nil),       // 3: detection.GetServiceInfoResponse
	(*DetectionInitRequest)(nil),         // 4: detection.DetectionInitRequest
	(*DetectionInitResponse)(nil),        // 5: detection.DetectionInitResponse
	(*DetectionCleanupRequest)(nil),      // 6: detection.DetectionCleanupRequest
	(*DetectionCleanupResponse)(nil),     // 7: detection.DetectionCleanupResponse
	(*DetectionGetItemsRequest)(nil),     // 8: detection.DetectionGetItemsRequest
	(*DetectionGetItemsResponse)(nil),    // 9: detection.DetectionGetItemsResponse
	(*DetectionStartRequest)(nil),        // 10: detection.DetectionStartRequest
	(*DetectionStartStreamResponse)(nil), // 11: detection.DetectionStartStreamResponse
	(*DetectionStopRequest)(nil),         // 12: detection.DetectionStopRequest
	(*DetectionStopResponse)(nil),        // 13: detection.DetectionStopResponse
}
var file_api_detection_detection_proto_depIdxs = []int32{
	0,  // 0: detection.GetServiceInfoResponse.services:type_name -> detection.ServiceInfo
	1,  // 1: detection.DetectionGetItemsResponse.items:type_name -> detection.DetectionItem
	2,  // 2: detection.BaseDetectionService.GetServiceInfo:input_type -> detection.GetServiceInfoRequest
	4,  // 3: detection.BaseDetectionService.DetectionInitialize:input_type -> detection.DetectionInitRequest
	6,  // 4: detection.BaseDetectionService.DetectionCleanup:input_type -> detection.DetectionCleanupRequest
	8,  // 5: detection.BaseDetectionService.DetectionGetItems:input_type -> detection.DetectionGetItemsRequest
	10, // 6: detection.BaseDetectionService.DetectionStart:input_type -> detection.DetectionStartRequest
	12, // 7: detection.BaseDetectionService.DetectionStop:input_type -> detection.DetectionStopRequest
	3,  // 8: detection.BaseDetectionService.GetServiceInfo:output_type -> detection.GetServiceInfoResponse
	5,  // 9: detection.BaseDetectionService.DetectionInitialize:output_type -> detection.DetectionInitResponse
	7,  // 10: detection.BaseDetectionService.DetectionCleanup:output_type -> detection.DetectionCleanupResponse
	9,  // 11: detection.BaseDetectionService.DetectionGetItems:output_type -> detection.DetectionGetItemsResponse
	11, // 12: detection.BaseDetectionService.DetectionStart:output_type -> detection.DetectionStartStreamResponse
	13, // 13: detection.BaseDetectionService.DetectionStop:output_type -> detection.DetectionStopResponse
	8,  // [8:14] is the sub-list for method output_type
	2,  // [2:8] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_api_detection_detection_proto_init() }
func file_api_detection_detection_proto_init() {
	if File_api_detection_detection_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_detection_detection_proto_rawDesc), len(file_api_detection_detection_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_detection_detection_proto_goTypes,
		DependencyIndexes: file_api_detection_detection_proto_depIdxs,
		MessageInfos:      file_api_detection_detection_proto_msgTypes,
	}.Build()
	File_api_detection_detection_proto = out.File
	file_api_detection_detection_proto_goTypes = nil
	file_api_detection_detection_proto_depIdxs = nil
}
