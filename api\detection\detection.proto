syntax = "proto3";

package detection;

option go_package = "./detection";

// 基础检测服务定义
service BaseDetectionService {
    rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
    rpc DetectionInitialize(DetectionInitRequest) returns (DetectionInitResponse);
    rpc DetectionCleanup(DetectionCleanupRequest) returns (DetectionCleanupResponse);
    rpc DetectionGetItems(DetectionGetItemsRequest) returns (DetectionGetItemsResponse);
    rpc DetectionStart(DetectionStartRequest) returns (stream DetectionStartStreamResponse);
    rpc DetectionStop(DetectionStopRequest) returns (DetectionStopResponse);
}

// 服务信息
message ServiceInfo {
    string name = 1;        // 服务名称
    string version = 2;     // 服务版本
    string description = 3; // 服务描述
}

// 检测项
message DetectionItem {
    int32 id = 1;      // 检测项ID
    string name = 2;    // 检测项名称
    string description = 3; // 检测项描述
}

// 服务信息获取请求
message GetServiceInfoRequest {
    string name = 1;    // 检测服务名称，如果为空("")则获取所有服务信息
}

// 服务信息获取响应
message GetServiceInfoResponse {
    repeated ServiceInfo services = 1;
}

// 检测初始化请求
message DetectionInitRequest {
    string name = 1;    // 检测服务名称
    string config = 2;  // 检测配置, JSON 格式（待定）
}

// 检测初始化响应
message DetectionInitResponse {
    string token = 1;   // 检测令牌，用于后续访问检测服务
}

// 检测清理请求
message DetectionCleanupRequest {
    string token = 1;   // 检测令牌
}

// 检测清理响应
message DetectionCleanupResponse {}

// 检测项获取请求
message DetectionGetItemsRequest {
    string token = 1;   // 检测令牌
}

// 检测项获取响应
message DetectionGetItemsResponse {
    repeated DetectionItem items = 1;
}

// 检测启动请求
message DetectionStartRequest {
    string token = 1;   // 检测令牌
    repeated int32 item_ids = 2;  // 检测项 ID 集, 如果为空则启动所有检测项
}

// 检测启动响应（服务端流式响应）
message DetectionStartStreamResponse {
    int32 item_id = 1;      // 检测项 ID
    int32 message_type = 2; // 消息类型 -> 0: 检测项开始, 1: 检测项进度, 3: 检测项过程消息, 4: 检测项成功消息, 4: 检测项失败消息
    string message = 3;     // 消息内容
}

// 检测停止请求
message DetectionStopRequest {
    string token = 1;   // 检测令牌
}

// 检测停止响应
message DetectionStopResponse {}
