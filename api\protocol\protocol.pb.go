// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/protocol/protocol.proto

package protocol

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务信息
type ServiceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`               // 服务名称
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`         // 服务版本
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 服务描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceInfo) Reset() {
	*x = ServiceInfo{}
	mi := &file_api_protocol_protocol_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfo) ProtoMessage() {}

func (x *ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfo.ProtoReflect.Descriptor instead.
func (*ServiceInfo) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ServiceInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 服务信息获取请求
type GetServiceInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 协议服务名称，如果为空("")则获取所有服务信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoRequest) Reset() {
	*x = GetServiceInfoRequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoRequest) ProtoMessage() {}

func (x *GetServiceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{1}
}

func (x *GetServiceInfoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 服务信息获取响应
type GetServiceInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []*ServiceInfo         `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoResponse) Reset() {
	*x = GetServiceInfoResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoResponse) ProtoMessage() {}

func (x *GetServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{2}
}

func (x *GetServiceInfoResponse) GetServices() []*ServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

// 通讯协议组织请求 Communication Protocol Organization
type CPORequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`    // 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
	Params        string                 `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"` // 组织参数, JSON 格式（待补充完善，根据具体协议类型，例 Modbus 需要寄存器类型、地址、数据等）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPORequest) Reset() {
	*x = CPORequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPORequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPORequest) ProtoMessage() {}

func (x *CPORequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPORequest.ProtoReflect.Descriptor instead.
func (*CPORequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{3}
}

func (x *CPORequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CPORequest) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

// 协议组织响应
type CPOResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Frame         string                 `protobuf:"bytes,1,opt,name=frame,proto3" json:"frame,omitempty"` // 组织后的数据, 字符串格式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPOResponse) Reset() {
	*x = CPOResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPOResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPOResponse) ProtoMessage() {}

func (x *CPOResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPOResponse.ProtoReflect.Descriptor instead.
func (*CPOResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{4}
}

func (x *CPOResponse) GetFrame() string {
	if x != nil {
		return x.Frame
	}
	return ""
}

// 通讯协议解析请求 Communication Protocol Parsing
type CPPRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`  // 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
	Frame         string                 `protobuf:"bytes,2,opt,name=frame,proto3" json:"frame,omitempty"` // 待解析的数据, 字符串格式
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPPRequest) Reset() {
	*x = CPPRequest{}
	mi := &file_api_protocol_protocol_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPPRequest) ProtoMessage() {}

func (x *CPPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPPRequest.ProtoReflect.Descriptor instead.
func (*CPPRequest) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{5}
}

func (x *CPPRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CPPRequest) GetFrame() string {
	if x != nil {
		return x.Frame
	}
	return ""
}

// 协议解析响应
type CPPResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 解析后的数据, JSON 格式（待补充，根据具体协议类型）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CPPResponse) Reset() {
	*x = CPPResponse{}
	mi := &file_api_protocol_protocol_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CPPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CPPResponse) ProtoMessage() {}

func (x *CPPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protocol_protocol_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CPPResponse.ProtoReflect.Descriptor instead.
func (*CPPResponse) Descriptor() ([]byte, []int) {
	return file_api_protocol_protocol_proto_rawDescGZIP(), []int{6}
}

func (x *CPPResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_api_protocol_protocol_proto protoreflect.FileDescriptor

const file_api_protocol_protocol_proto_rawDesc = "" +
	"\n" +
	"\x1bapi/protocol/protocol.proto\x12\bprotocol\"]\n" +
	"\vServiceInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"+\n" +
	"\x15GetServiceInfoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"K\n" +
	"\x16GetServiceInfoResponse\x121\n" +
	"\bservices\x18\x01 \x03(\v2\x15.protocol.ServiceInfoR\bservices\"8\n" +
	"\n" +
	"CPORequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x16\n" +
	"\x06params\x18\x02 \x01(\tR\x06params\"#\n" +
	"\vCPOResponse\x12\x14\n" +
	"\x05frame\x18\x01 \x01(\tR\x05frame\"6\n" +
	"\n" +
	"CPPRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x14\n" +
	"\x05frame\x18\x02 \x01(\tR\x05frame\"!\n" +
	"\vCPPResponse\x12\x12\n" +
	"\x04data\x18\x01 \x01(\tR\x04data2\xef\x01\n" +
	"\x13BaseProtocolService\x12S\n" +
	"\x0eGetServiceInfo\x12\x1f.protocol.GetServiceInfoRequest\x1a .protocol.GetServiceInfoResponse\x12C\n" +
	"\x14ProtocolOrganization\x12\x14.protocol.CPORequest\x1a\x15.protocol.CPOResponse\x12>\n" +
	"\x0fProtocolParsing\x12\x14.protocol.CPPRequest\x1a\x15.protocol.CPPResponseB\fZ\n" +
	"./protocolb\x06proto3"

var (
	file_api_protocol_protocol_proto_rawDescOnce sync.Once
	file_api_protocol_protocol_proto_rawDescData []byte
)

func file_api_protocol_protocol_proto_rawDescGZIP() []byte {
	file_api_protocol_protocol_proto_rawDescOnce.Do(func() {
		file_api_protocol_protocol_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_protocol_protocol_proto_rawDesc), len(file_api_protocol_protocol_proto_rawDesc)))
	})
	return file_api_protocol_protocol_proto_rawDescData
}

var file_api_protocol_protocol_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_protocol_protocol_proto_goTypes = []any{
	(*ServiceInfo)(nil),            // 0: protocol.ServiceInfo
	(*GetServiceInfoRequest)(nil),  // 1: protocol.GetServiceInfoRequest
	(*GetServiceInfoResponse)(nil), // 2: protocol.GetServiceInfoResponse
	(*CPORequest)(nil),             // 3: protocol.CPORequest
	(*CPOResponse)(nil),            // 4: protocol.CPOResponse
	(*CPPRequest)(nil),             // 5: protocol.CPPRequest
	(*CPPResponse)(nil),            // 6: protocol.CPPResponse
}
var file_api_protocol_protocol_proto_depIdxs = []int32{
	0, // 0: protocol.GetServiceInfoResponse.services:type_name -> protocol.ServiceInfo
	1, // 1: protocol.BaseProtocolService.GetServiceInfo:input_type -> protocol.GetServiceInfoRequest
	3, // 2: protocol.BaseProtocolService.ProtocolOrganization:input_type -> protocol.CPORequest
	5, // 3: protocol.BaseProtocolService.ProtocolParsing:input_type -> protocol.CPPRequest
	2, // 4: protocol.BaseProtocolService.GetServiceInfo:output_type -> protocol.GetServiceInfoResponse
	4, // 5: protocol.BaseProtocolService.ProtocolOrganization:output_type -> protocol.CPOResponse
	6, // 6: protocol.BaseProtocolService.ProtocolParsing:output_type -> protocol.CPPResponse
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_protocol_protocol_proto_init() }
func file_api_protocol_protocol_proto_init() {
	if File_api_protocol_protocol_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_protocol_protocol_proto_rawDesc), len(file_api_protocol_protocol_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_protocol_protocol_proto_goTypes,
		DependencyIndexes: file_api_protocol_protocol_proto_depIdxs,
		MessageInfos:      file_api_protocol_protocol_proto_msgTypes,
	}.Build()
	File_api_protocol_protocol_proto = out.File
	file_api_protocol_protocol_proto_goTypes = nil
	file_api_protocol_protocol_proto_depIdxs = nil
}
