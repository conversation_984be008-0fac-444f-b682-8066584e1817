package modbus

import (
	"encoding/binary"
	"errors"
	"fmt"
	"sync"
	"time"

	"tp.service/internal/common/serial"
)

// 功能码
const (
	FuncReadCoils              byte = 0x01
	FuncReadDiscreteInputs     byte = 0x02
	FuncReadHoldingRegisters   byte = 0x03
	FuncReadInputRegisters     byte = 0x04
	FuncWriteSingleCoil        byte = 0x05
	FuncWriteSingleRegister    byte = 0x06
	FuncWriteMultipleCoils     byte = 0x0F
	FuncWriteMultipleRegisters byte = 0x10
)

// 错误码
const (
	ErrIllegalFunction                    byte = 0x01
	ErrIllegalDataAddress                 byte = 0x02
	ErrIllegalDataValue                   byte = 0x03
	ErrServerDeviceFailure                byte = 0x04
	ErrAcknowledge                        byte = 0x05
	ErrServerDeviceBusy                   byte = 0x06
	ErrMemoryParityError                  byte = 0x08
	ErrGatewayPathUnavailable             byte = 0x0A
	ErrGatewayTargetDeviceFailedToRespond byte = 0x0B
)

// ModbusClient 表示Modbus客户端
type ModbusClient struct {
	serialPort *serial.SerialPort
	mutex      sync.Mutex
	timeout    time.Duration
}

// NewModbusClient 创建一个新的Modbus客户端
func NewModbusClient(config serial.SerialConfig) *ModbusClient {
	return &ModbusClient{
		serialPort: serial.NewSerialPort(config),
		timeout:    time.Second * 3, // 默认超时3秒
	}
}

// SetTimeout 设置超时时间
func (c *ModbusClient) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
}

// Connect 连接设备
func (c *ModbusClient) Connect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 打开串口
	if err := c.serialPort.Open(); err != nil {
		return err
	}

	// 清空缓冲区
	if err := c.serialPort.Flush(); err != nil {
		return err
	}

	return nil
}

// Disconnect 断开连接
func (c *ModbusClient) Disconnect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.serialPort.IsOpen() {
		return nil
	}

	return c.serialPort.Close()
}

// ReadHoldingRegisters 读取保持寄存器
func (c *ModbusClient) ReadHoldingRegisters(slaveID byte, startAddress uint16, quantity uint16) ([]byte, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.serialPort.IsOpen() {
		return nil, errors.New("串口未打开")
	}

	// 清空缓冲区
	if err := c.serialPort.Flush(); err != nil {
		return nil, err
	}

	// 构建请求
	request := make([]byte, 8)
	request[0] = slaveID
	request[1] = FuncReadHoldingRegisters
	binary.BigEndian.PutUint16(request[2:4], startAddress)
	binary.BigEndian.PutUint16(request[4:6], quantity)

	// 计算CRC
	crc := calcCRC16(request[:6])
	request[6] = byte(crc & 0xFF)
	request[7] = byte(crc >> 8)

	// 写入请求
	_, err := c.serialPort.Write(request)
	if err != nil {
		return nil, err
	}

	// 读取响应
	// 响应格式: 从站地址(1) + 功能码(1) + 字节数(1) + 数据(n) + CRC(2)
	// 最小响应长度: 5字节 (从站地址 + 功能码 + 字节数 + CRC)
	response := make([]byte, 5+quantity*2)
	n, err := c.serialPort.ReadWithTimeout(response, c.timeout)
	if err != nil {
		return nil, err
	}

	// 检查响应长度
	if n < 5 {
		return nil, errors.New("响应长度不足")
	}

	// 检查从站地址
	if response[0] != slaveID {
		return nil, fmt.Errorf("从站地址不匹配，期望%d，实际%d", slaveID, response[0])
	}

	// 检查功能码
	if response[1] != FuncReadHoldingRegisters {
		// 检查是否是异常响应
		if response[1] == FuncReadHoldingRegisters+0x80 {
			if n >= 3 {
				return nil, fmt.Errorf("Modbus异常: %d", response[2])
			}
			return nil, errors.New("Modbus异常响应格式错误")
		}
		return nil, fmt.Errorf("功能码不匹配，期望%d，实际%d", FuncReadHoldingRegisters, response[1])
	}

	// 检查字节数
	byteCount := response[2]
	if byteCount != uint8(quantity*2) {
		return nil, fmt.Errorf("字节数不匹配，期望%d，实际%d", quantity*2, byteCount)
	}

	// 检查响应长度是否足够
	expectedLength := 5 + byteCount
	if n < int(expectedLength) {
		return nil, fmt.Errorf("响应长度不足，期望%d，实际%d", expectedLength, n)
	}

	// 检查CRC
	responseCRC := binary.LittleEndian.Uint16(response[3+byteCount : 5+byteCount])
	calculatedCRC := calcCRC16(response[:3+byteCount])
	if responseCRC != calculatedCRC {
		return nil, fmt.Errorf("CRC校验错误，期望0x%04X，计算得到0x%04X", responseCRC, calculatedCRC)
	}

	// 返回数据
	return response[3 : 3+byteCount], nil
}

// WriteHoldingRegisters 写入保持寄存器
func (c *ModbusClient) WriteHoldingRegisters(slaveID byte, startAddress uint16, values []uint16) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.serialPort.IsOpen() {
		return errors.New("串口未打开")
	}

	// 清空缓冲区
	if err := c.serialPort.Flush(); err != nil {
		return err
	}

	// 构建请求
	// 请求格式: 从站地址(1) + 功能码(1) + 起始地址(2) + 寄存器数量(2) + 字节数(1) + 数据(n) + CRC(2)
	quantity := uint16(len(values))
	byteCount := byte(quantity * 2)
	request := make([]byte, 7+byteCount+2)
	request[0] = slaveID
	request[1] = FuncWriteMultipleRegisters
	binary.BigEndian.PutUint16(request[2:4], startAddress)
	binary.BigEndian.PutUint16(request[4:6], quantity)
	request[6] = byteCount

	// 写入数据
	for i, value := range values {
		binary.BigEndian.PutUint16(request[7+i*2:9+i*2], value)
	}

	// 计算CRC
	crc := calcCRC16(request[:7+byteCount])
	request[7+byteCount] = byte(crc & 0xFF)
	request[7+byteCount+1] = byte(crc >> 8)

	// 写入请求
	_, err := c.serialPort.Write(request)
	if err != nil {
		return err
	}

	// 读取响应
	// 响应格式: 从站地址(1) + 功能码(1) + 起始地址(2) + 寄存器数量(2) + CRC(2)
	response := make([]byte, 8)
	n, err := c.serialPort.ReadWithTimeout(response, c.timeout)
	if err != nil {
		return err
	}

	// 检查响应长度
	if n < 8 {
		return errors.New("响应长度不足")
	}

	// 检查从站地址
	if response[0] != slaveID {
		return fmt.Errorf("从站地址不匹配，期望%d，实际%d", slaveID, response[0])
	}

	// 检查功能码
	if response[1] != FuncWriteMultipleRegisters {
		// 检查是否是异常响应
		if response[1] == FuncWriteMultipleRegisters+0x80 {
			if n >= 3 {
				return fmt.Errorf("Modbus异常: %d", response[2])
			}
			return errors.New("Modbus异常响应格式错误")
		}
		return fmt.Errorf("功能码不匹配，期望%d，实际%d", FuncWriteMultipleRegisters, response[1])
	}

	// 检查起始地址
	responseStartAddress := binary.BigEndian.Uint16(response[2:4])
	if responseStartAddress != startAddress {
		return fmt.Errorf("起始地址不匹配，期望%d，实际%d", startAddress, responseStartAddress)
	}

	// 检查寄存器数量
	responseQuantity := binary.BigEndian.Uint16(response[4:6])
	if responseQuantity != quantity {
		return fmt.Errorf("寄存器数量不匹配，期望%d，实际%d", quantity, responseQuantity)
	}

	// 检查CRC
	responseCRC := binary.LittleEndian.Uint16(response[6:8])
	calculatedCRC := calcCRC16(response[:6])
	if responseCRC != calculatedCRC {
		return fmt.Errorf("CRC校验错误，期望0x%04X，计算得到0x%04X", responseCRC, calculatedCRC)
	}

	return nil
}

// ReadInputRegisters 读取输入寄存器
func (c *ModbusClient) ReadInputRegisters(slaveID byte, startAddress uint16, quantity uint16) ([]byte, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.serialPort.IsOpen() {
		return nil, errors.New("串口未打开")
	}

	// 清空缓冲区
	if err := c.serialPort.Flush(); err != nil {
		return nil, err
	}

	// 构建请求
	request := make([]byte, 8)
	request[0] = slaveID
	request[1] = FuncReadInputRegisters
	binary.BigEndian.PutUint16(request[2:4], startAddress)
	binary.BigEndian.PutUint16(request[4:6], quantity)

	// 计算CRC
	crc := calcCRC16(request[:6])
	request[6] = byte(crc & 0xFF)
	request[7] = byte(crc >> 8)

	// 写入请求
	_, err := c.serialPort.Write(request)
	if err != nil {
		return nil, err
	}

	// 读取响应
	// 响应格式: 从站地址(1) + 功能码(1) + 字节数(1) + 数据(n) + CRC(2)
	response := make([]byte, 5+quantity*2)
	n, err := c.serialPort.ReadWithTimeout(response, c.timeout)
	if err != nil {
		return nil, err
	}

	// 检查响应长度
	if n < 5 {
		return nil, errors.New("响应长度不足")
	}

	// 检查从站地址
	if response[0] != slaveID {
		return nil, fmt.Errorf("从站地址不匹配，期望%d，实际%d", slaveID, response[0])
	}

	// 检查功能码
	if response[1] != FuncReadInputRegisters {
		// 检查是否是异常响应
		if response[1] == FuncReadInputRegisters+0x80 {
			if n >= 3 {
				return nil, fmt.Errorf("Modbus异常: %d", response[2])
			}
			return nil, errors.New("Modbus异常响应格式错误")
		}
		return nil, fmt.Errorf("功能码不匹配，期望%d，实际%d", FuncReadInputRegisters, response[1])
	}

	// 检查字节数
	byteCount := response[2]
	if byteCount != uint8(quantity*2) {
		return nil, fmt.Errorf("字节数不匹配，期望%d，实际%d", quantity*2, byteCount)
	}

	// 检查响应长度是否足够
	expectedLength := 5 + byteCount
	if n < int(expectedLength) {
		return nil, fmt.Errorf("响应长度不足，期望%d，实际%d", expectedLength, n)
	}

	// 检查CRC
	responseCRC := binary.LittleEndian.Uint16(response[3+byteCount : 5+byteCount])
	calculatedCRC := calcCRC16(response[:3+byteCount])
	if responseCRC != calculatedCRC {
		return nil, fmt.Errorf("CRC校验错误，期望0x%04X，计算得到0x%04X", responseCRC, calculatedCRC)
	}

	// 返回数据
	return response[3 : 3+byteCount], nil
}

// WriteSingleRegister 写入单个寄存器
func (c *ModbusClient) WriteSingleRegister(slaveID byte, address uint16, value uint16) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.serialPort.IsOpen() {
		return errors.New("串口未打开")
	}

	// 清空缓冲区
	if err := c.serialPort.Flush(); err != nil {
		return err
	}

	// 构建请求
	request := make([]byte, 8)
	request[0] = slaveID
	request[1] = FuncWriteSingleRegister
	binary.BigEndian.PutUint16(request[2:4], address)
	binary.BigEndian.PutUint16(request[4:6], value)

	// 计算CRC
	crc := calcCRC16(request[:6])
	request[6] = byte(crc & 0xFF)
	request[7] = byte(crc >> 8)

	// 写入请求
	_, err := c.serialPort.Write(request)
	if err != nil {
		return err
	}

	// 读取响应
	// 响应格式: 从站地址(1) + 功能码(1) + 寄存器地址(2) + 寄存器值(2) + CRC(2)
	response := make([]byte, 8)
	n, err := c.serialPort.ReadWithTimeout(response, c.timeout)
	if err != nil {
		return err
	}

	// 检查响应长度
	if n < 8 {
		return errors.New("响应长度不足")
	}

	// 检查从站地址
	if response[0] != slaveID {
		return fmt.Errorf("从站地址不匹配，期望%d，实际%d", slaveID, response[0])
	}

	// 检查功能码
	if response[1] != FuncWriteSingleRegister {
		// 检查是否是异常响应
		if response[1] == FuncWriteSingleRegister+0x80 {
			if n >= 3 {
				return fmt.Errorf("Modbus异常: %d", response[2])
			}
			return errors.New("Modbus异常响应格式错误")
		}
		return fmt.Errorf("功能码不匹配，期望%d，实际%d", FuncWriteSingleRegister, response[1])
	}

	// 检查寄存器地址
	responseAddress := binary.BigEndian.Uint16(response[2:4])
	if responseAddress != address {
		return fmt.Errorf("寄存器地址不匹配，期望%d，实际%d", address, responseAddress)
	}

	// 检查寄存器值
	responseValue := binary.BigEndian.Uint16(response[4:6])
	if responseValue != value {
		return fmt.Errorf("寄存器值不匹配，期望%d，实际%d", value, responseValue)
	}

	// 检查CRC
	responseCRC := binary.LittleEndian.Uint16(response[6:8])
	calculatedCRC := calcCRC16(response[:6])
	if responseCRC != calculatedCRC {
		return fmt.Errorf("CRC校验错误，期望0x%04X，计算得到0x%04X", responseCRC, calculatedCRC)
	}

	return nil
}

// IsConnected 检查是否已连接
func (c *ModbusClient) IsConnected() bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.serialPort.IsOpen()
}

// GetSerialPort 获取串口
func (c *ModbusClient) GetSerialPort() *serial.SerialPort {
	return c.serialPort
}

// calcCRC16 计算Modbus CRC16校验和
func calcCRC16(data []byte) uint16 {
	crc := uint16(0xFFFF)
	for _, b := range data {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&0x0001 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}
