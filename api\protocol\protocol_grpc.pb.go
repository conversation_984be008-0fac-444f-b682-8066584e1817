// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/protocol/protocol.proto

package protocol

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BaseProtocolService_GetServiceInfo_FullMethodName       = "/protocol.BaseProtocolService/GetServiceInfo"
	BaseProtocolService_ProtocolOrganization_FullMethodName = "/protocol.BaseProtocolService/ProtocolOrganization"
	BaseProtocolService_ProtocolParsing_FullMethodName      = "/protocol.BaseProtocolService/ProtocolParsing"
)

// BaseProtocolServiceClient is the client API for BaseProtocolService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 基础协议服务定义
type BaseProtocolServiceClient interface {
	GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error)
	ProtocolOrganization(ctx context.Context, in *CPORequest, opts ...grpc.CallOption) (*CPOResponse, error)
	ProtocolParsing(ctx context.Context, in *CPPRequest, opts ...grpc.CallOption) (*CPPResponse, error)
}

type baseProtocolServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBaseProtocolServiceClient(cc grpc.ClientConnInterface) BaseProtocolServiceClient {
	return &baseProtocolServiceClient{cc}
}

func (c *baseProtocolServiceClient) GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceInfoResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_GetServiceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseProtocolServiceClient) ProtocolOrganization(ctx context.Context, in *CPORequest, opts ...grpc.CallOption) (*CPOResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPOResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_ProtocolOrganization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseProtocolServiceClient) ProtocolParsing(ctx context.Context, in *CPPRequest, opts ...grpc.CallOption) (*CPPResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CPPResponse)
	err := c.cc.Invoke(ctx, BaseProtocolService_ProtocolParsing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BaseProtocolServiceServer is the server API for BaseProtocolService service.
// All implementations must embed UnimplementedBaseProtocolServiceServer
// for forward compatibility.
//
// 基础协议服务定义
type BaseProtocolServiceServer interface {
	GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error)
	ProtocolOrganization(context.Context, *CPORequest) (*CPOResponse, error)
	ProtocolParsing(context.Context, *CPPRequest) (*CPPResponse, error)
	mustEmbedUnimplementedBaseProtocolServiceServer()
}

// UnimplementedBaseProtocolServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBaseProtocolServiceServer struct{}

func (UnimplementedBaseProtocolServiceServer) GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceInfo not implemented")
}
func (UnimplementedBaseProtocolServiceServer) ProtocolOrganization(context.Context, *CPORequest) (*CPOResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolOrganization not implemented")
}
func (UnimplementedBaseProtocolServiceServer) ProtocolParsing(context.Context, *CPPRequest) (*CPPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProtocolParsing not implemented")
}
func (UnimplementedBaseProtocolServiceServer) mustEmbedUnimplementedBaseProtocolServiceServer() {}
func (UnimplementedBaseProtocolServiceServer) testEmbeddedByValue()                             {}

// UnsafeBaseProtocolServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BaseProtocolServiceServer will
// result in compilation errors.
type UnsafeBaseProtocolServiceServer interface {
	mustEmbedUnimplementedBaseProtocolServiceServer()
}

func RegisterBaseProtocolServiceServer(s grpc.ServiceRegistrar, srv BaseProtocolServiceServer) {
	// If the following call pancis, it indicates UnimplementedBaseProtocolServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BaseProtocolService_ServiceDesc, srv)
}

func _BaseProtocolService_GetServiceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).GetServiceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_GetServiceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).GetServiceInfo(ctx, req.(*GetServiceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseProtocolService_ProtocolOrganization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPORequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).ProtocolOrganization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_ProtocolOrganization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).ProtocolOrganization(ctx, req.(*CPORequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseProtocolService_ProtocolParsing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CPPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseProtocolServiceServer).ProtocolParsing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseProtocolService_ProtocolParsing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseProtocolServiceServer).ProtocolParsing(ctx, req.(*CPPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BaseProtocolService_ServiceDesc is the grpc.ServiceDesc for BaseProtocolService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BaseProtocolService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "protocol.BaseProtocolService",
	HandlerType: (*BaseProtocolServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServiceInfo",
			Handler:    _BaseProtocolService_GetServiceInfo_Handler,
		},
		{
			MethodName: "ProtocolOrganization",
			Handler:    _BaseProtocolService_ProtocolOrganization_Handler,
		},
		{
			MethodName: "ProtocolParsing",
			Handler:    _BaseProtocolService_ProtocolParsing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/protocol/protocol.proto",
}
