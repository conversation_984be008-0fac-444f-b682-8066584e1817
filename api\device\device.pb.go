// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/device/device.proto

package device

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务信息
type ServiceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`               // 服务名称
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`         // 服务版本
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 服务描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceInfo) Reset() {
	*x = ServiceInfo{}
	mi := &file_api_device_device_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInfo) ProtoMessage() {}

func (x *ServiceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInfo.ProtoReflect.Descriptor instead.
func (*ServiceInfo) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ServiceInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 设备信息
type DeviceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`                                 // 设备类型, 0x01: 电能表, 0x10: 逆变器, 0x11: 充电桩, 0x12: 数据采集器
	Position      int32                  `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                         // 设备检测位
	SerialNum     int32                  `protobuf:"varint,3,opt,name=serial_num,json=serialNum,proto3" json:"serial_num,omitempty"`      // 设备序号，默认为 1，应对一个检测位多个同类型设备情况
	CommonType    int32                  `protobuf:"varint,4,opt,name=common_type,json=commonType,proto3" json:"common_type,omitempty"`   // 通信类型， 默认 0 串口
	CommonParam   string                 `protobuf:"bytes,5,opt,name=common_param,json=commonParam,proto3" json:"common_param,omitempty"` // 通讯参数, 串口：COM1,9600,8,N,1
	Protocol      string                 `protobuf:"bytes,6,opt,name=protocol,proto3" json:"protocol,omitempty"`                          // 设备协议
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	mi := &file_api_device_device_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DeviceInfo) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *DeviceInfo) GetSerialNum() int32 {
	if x != nil {
		return x.SerialNum
	}
	return 0
}

func (x *DeviceInfo) GetCommonType() int32 {
	if x != nil {
		return x.CommonType
	}
	return 0
}

func (x *DeviceInfo) GetCommonParam() string {
	if x != nil {
		return x.CommonParam
	}
	return ""
}

func (x *DeviceInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 设备参数数据
type DeviceData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ParamType     int32                  `protobuf:"varint,1,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"` // 参数类型
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`                           // 参数值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceData) Reset() {
	*x = DeviceData{}
	mi := &file_api_device_device_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceData) ProtoMessage() {}

func (x *DeviceData) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceData.ProtoReflect.Descriptor instead.
func (*DeviceData) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{2}
}

func (x *DeviceData) GetParamType() int32 {
	if x != nil {
		return x.ParamType
	}
	return 0
}

func (x *DeviceData) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 服务信息获取请求
type GetServiceInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 设备服务名称，如果为空("")则获取所有服务信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoRequest) Reset() {
	*x = GetServiceInfoRequest{}
	mi := &file_api_device_device_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoRequest) ProtoMessage() {}

func (x *GetServiceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{3}
}

func (x *GetServiceInfoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 服务信息获取响应
type GetServiceInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []*ServiceInfo         `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoResponse) Reset() {
	*x = GetServiceInfoResponse{}
	mi := &file_api_device_device_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoResponse) ProtoMessage() {}

func (x *GetServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{4}
}

func (x *GetServiceInfoResponse) GetServices() []*ServiceInfo {
	if x != nil {
		return x.Services
	}
	return nil
}

// 设备（模拟）初始化请求
type DeviceInitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Devices       []*DeviceInfo          `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceInitRequest) Reset() {
	*x = DeviceInitRequest{}
	mi := &file_api_device_device_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceInitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInitRequest) ProtoMessage() {}

func (x *DeviceInitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInitRequest.ProtoReflect.Descriptor instead.
func (*DeviceInitRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{5}
}

func (x *DeviceInitRequest) GetDevices() []*DeviceInfo {
	if x != nil {
		return x.Devices
	}
	return nil
}

// 设备初始化响应
type DeviceInitResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Devices       []*DeviceInfo          `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`                                  // 初始化成功的设备信息
	FailedDevices []*DeviceInfo          `protobuf:"bytes,2,rep,name=failed_devices,json=failedDevices,proto3" json:"failed_devices,omitempty"` // 初始化失败的设备信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceInitResponse) Reset() {
	*x = DeviceInitResponse{}
	mi := &file_api_device_device_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceInitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInitResponse) ProtoMessage() {}

func (x *DeviceInitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInitResponse.ProtoReflect.Descriptor instead.
func (*DeviceInitResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{6}
}

func (x *DeviceInitResponse) GetDevices() []*DeviceInfo {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *DeviceInitResponse) GetFailedDevices() []*DeviceInfo {
	if x != nil {
		return x.FailedDevices
	}
	return nil
}

// 设备清理请求
type DeviceCleanupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Devices       []*DeviceInfo          `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceCleanupRequest) Reset() {
	*x = DeviceCleanupRequest{}
	mi := &file_api_device_device_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceCleanupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceCleanupRequest) ProtoMessage() {}

func (x *DeviceCleanupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceCleanupRequest.ProtoReflect.Descriptor instead.
func (*DeviceCleanupRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{7}
}

func (x *DeviceCleanupRequest) GetDevices() []*DeviceInfo {
	if x != nil {
		return x.Devices
	}
	return nil
}

// 设备清理响应
type DeviceCleanupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Devices       []*DeviceInfo          `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`                                  // 清理成功的设备信息
	FailedDevices []*DeviceInfo          `protobuf:"bytes,2,rep,name=failed_devices,json=failedDevices,proto3" json:"failed_devices,omitempty"` // 清理失败的设备信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceCleanupResponse) Reset() {
	*x = DeviceCleanupResponse{}
	mi := &file_api_device_device_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceCleanupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceCleanupResponse) ProtoMessage() {}

func (x *DeviceCleanupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceCleanupResponse.ProtoReflect.Descriptor instead.
func (*DeviceCleanupResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{8}
}

func (x *DeviceCleanupResponse) GetDevices() []*DeviceInfo {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *DeviceCleanupResponse) GetFailedDevices() []*DeviceInfo {
	if x != nil {
		return x.FailedDevices
	}
	return nil
}

// 设备通信参数修改请求
type DeviceModifyCommParamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`                                 // 设备类型
	Position      int32                  `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                         // 设备检测位
	SerialNum     int32                  `protobuf:"varint,3,opt,name=serial_num,json=serialNum,proto3" json:"serial_num,omitempty"`      // 设备序号
	CommonType    int32                  `protobuf:"varint,4,opt,name=common_type,json=commonType,proto3" json:"common_type,omitempty"`   // 通信类型， 默认 0 串口
	CommonParam   string                 `protobuf:"bytes,5,opt,name=common_param,json=commonParam,proto3" json:"common_param,omitempty"` // 通讯参数, 串口：COM1,9600,8,N,1
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceModifyCommParamRequest) Reset() {
	*x = DeviceModifyCommParamRequest{}
	mi := &file_api_device_device_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceModifyCommParamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceModifyCommParamRequest) ProtoMessage() {}

func (x *DeviceModifyCommParamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceModifyCommParamRequest.ProtoReflect.Descriptor instead.
func (*DeviceModifyCommParamRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{9}
}

func (x *DeviceModifyCommParamRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DeviceModifyCommParamRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *DeviceModifyCommParamRequest) GetSerialNum() int32 {
	if x != nil {
		return x.SerialNum
	}
	return 0
}

func (x *DeviceModifyCommParamRequest) GetCommonType() int32 {
	if x != nil {
		return x.CommonType
	}
	return 0
}

func (x *DeviceModifyCommParamRequest) GetCommonParam() string {
	if x != nil {
		return x.CommonParam
	}
	return ""
}

// 设备通信参数修改响应
type DeviceModifyCommParamResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceModifyCommParamResponse) Reset() {
	*x = DeviceModifyCommParamResponse{}
	mi := &file_api_device_device_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceModifyCommParamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceModifyCommParamResponse) ProtoMessage() {}

func (x *DeviceModifyCommParamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceModifyCommParamResponse.ProtoReflect.Descriptor instead.
func (*DeviceModifyCommParamResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{10}
}

// 设备协议修改请求
type DeviceModifyProtocolRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`                            // 设备类型
	Position      int32                  `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                    // 设备检测位
	SerialNum     int32                  `protobuf:"varint,3,opt,name=serial_num,json=serialNum,proto3" json:"serial_num,omitempty"` // 设备序号
	Protocol      string                 `protobuf:"bytes,4,opt,name=protocol,proto3" json:"protocol,omitempty"`                     // 设备协议
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceModifyProtocolRequest) Reset() {
	*x = DeviceModifyProtocolRequest{}
	mi := &file_api_device_device_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceModifyProtocolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceModifyProtocolRequest) ProtoMessage() {}

func (x *DeviceModifyProtocolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceModifyProtocolRequest.ProtoReflect.Descriptor instead.
func (*DeviceModifyProtocolRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{11}
}

func (x *DeviceModifyProtocolRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DeviceModifyProtocolRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *DeviceModifyProtocolRequest) GetSerialNum() int32 {
	if x != nil {
		return x.SerialNum
	}
	return 0
}

func (x *DeviceModifyProtocolRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 设备协议修改响应
type DeviceModifyProtocolResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceModifyProtocolResponse) Reset() {
	*x = DeviceModifyProtocolResponse{}
	mi := &file_api_device_device_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceModifyProtocolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceModifyProtocolResponse) ProtoMessage() {}

func (x *DeviceModifyProtocolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceModifyProtocolResponse.ProtoReflect.Descriptor instead.
func (*DeviceModifyProtocolResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{12}
}

// 设备数据获取请求
type DeviceGetDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`                                   // 设备类型
	Position      int32                  `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                           // 设备检测位
	SerialNum     int32                  `protobuf:"varint,3,opt,name=serial_num,json=serialNum,proto3" json:"serial_num,omitempty"`        // 设备序号
	ParamType     []int32                `protobuf:"varint,4,rep,packed,name=param_type,json=paramType,proto3" json:"param_type,omitempty"` // 参数类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceGetDataRequest) Reset() {
	*x = DeviceGetDataRequest{}
	mi := &file_api_device_device_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceGetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceGetDataRequest) ProtoMessage() {}

func (x *DeviceGetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceGetDataRequest.ProtoReflect.Descriptor instead.
func (*DeviceGetDataRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{13}
}

func (x *DeviceGetDataRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DeviceGetDataRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *DeviceGetDataRequest) GetSerialNum() int32 {
	if x != nil {
		return x.SerialNum
	}
	return 0
}

func (x *DeviceGetDataRequest) GetParamType() []int32 {
	if x != nil {
		return x.ParamType
	}
	return nil
}

// 设备数据获取响应
type DeviceGetDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*DeviceData          `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"` // 获取的设备数据集
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceGetDataResponse) Reset() {
	*x = DeviceGetDataResponse{}
	mi := &file_api_device_device_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceGetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceGetDataResponse) ProtoMessage() {}

func (x *DeviceGetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceGetDataResponse.ProtoReflect.Descriptor instead.
func (*DeviceGetDataResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{14}
}

func (x *DeviceGetDataResponse) GetData() []*DeviceData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 设备数据设置请求
type DeviceSetDataRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`                            // 设备类型
	Position      int32                  `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                    // 设备检测位
	SerialNum     int32                  `protobuf:"varint,3,opt,name=serial_num,json=serialNum,proto3" json:"serial_num,omitempty"` // 设备序号
	Data          []*DeviceData          `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`                             // 设置的设备数据集
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceSetDataRequest) Reset() {
	*x = DeviceSetDataRequest{}
	mi := &file_api_device_device_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceSetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceSetDataRequest) ProtoMessage() {}

func (x *DeviceSetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceSetDataRequest.ProtoReflect.Descriptor instead.
func (*DeviceSetDataRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{15}
}

func (x *DeviceSetDataRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DeviceSetDataRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *DeviceSetDataRequest) GetSerialNum() int32 {
	if x != nil {
		return x.SerialNum
	}
	return 0
}

func (x *DeviceSetDataRequest) GetData() []*DeviceData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 设备数据设置响应
type DeviceSetDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceSetDataResponse) Reset() {
	*x = DeviceSetDataResponse{}
	mi := &file_api_device_device_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceSetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceSetDataResponse) ProtoMessage() {}

func (x *DeviceSetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceSetDataResponse.ProtoReflect.Descriptor instead.
func (*DeviceSetDataResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{16}
}

// 设备通讯请求
type DeviceCommRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int32                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`                            // 设备类型
	Position      int32                  `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                    // 设备检测位
	SerialNum     int32                  `protobuf:"varint,3,opt,name=serial_num,json=serialNum,proto3" json:"serial_num,omitempty"` // 设备序号
	Data          string                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`                             // 通讯数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceCommRequest) Reset() {
	*x = DeviceCommRequest{}
	mi := &file_api_device_device_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceCommRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceCommRequest) ProtoMessage() {}

func (x *DeviceCommRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceCommRequest.ProtoReflect.Descriptor instead.
func (*DeviceCommRequest) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{17}
}

func (x *DeviceCommRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DeviceCommRequest) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *DeviceCommRequest) GetSerialNum() int32 {
	if x != nil {
		return x.SerialNum
	}
	return 0
}

func (x *DeviceCommRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// 设备通讯响应
type DeviceCommResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          string                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 通讯数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceCommResponse) Reset() {
	*x = DeviceCommResponse{}
	mi := &file_api_device_device_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceCommResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceCommResponse) ProtoMessage() {}

func (x *DeviceCommResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_device_device_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceCommResponse.ProtoReflect.Descriptor instead.
func (*DeviceCommResponse) Descriptor() ([]byte, []int) {
	return file_api_device_device_proto_rawDescGZIP(), []int{18}
}

func (x *DeviceCommResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_api_device_device_proto protoreflect.FileDescriptor

const file_api_device_device_proto_rawDesc = "" +
	"\n" +
	"\x17api/device/device.proto\x12\x06device\"]\n" +
	"\vServiceInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"\xbb\x01\n" +
	"\n" +
	"DeviceInfo\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x1a\n" +
	"\bposition\x18\x02 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"serial_num\x18\x03 \x01(\x05R\tserialNum\x12\x1f\n" +
	"\vcommon_type\x18\x04 \x01(\x05R\n" +
	"commonType\x12!\n" +
	"\fcommon_param\x18\x05 \x01(\tR\vcommonParam\x12\x1a\n" +
	"\bprotocol\x18\x06 \x01(\tR\bprotocol\"A\n" +
	"\n" +
	"DeviceData\x12\x1d\n" +
	"\n" +
	"param_type\x18\x01 \x01(\x05R\tparamType\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"+\n" +
	"\x15GetServiceInfoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"I\n" +
	"\x16GetServiceInfoResponse\x12/\n" +
	"\bservices\x18\x01 \x03(\v2\x13.device.ServiceInfoR\bservices\"A\n" +
	"\x11DeviceInitRequest\x12,\n" +
	"\adevices\x18\x01 \x03(\v2\x12.device.DeviceInfoR\adevices\"}\n" +
	"\x12DeviceInitResponse\x12,\n" +
	"\adevices\x18\x01 \x03(\v2\x12.device.DeviceInfoR\adevices\x129\n" +
	"\x0efailed_devices\x18\x02 \x03(\v2\x12.device.DeviceInfoR\rfailedDevices\"D\n" +
	"\x14DeviceCleanupRequest\x12,\n" +
	"\adevices\x18\x01 \x03(\v2\x12.device.DeviceInfoR\adevices\"\x80\x01\n" +
	"\x15DeviceCleanupResponse\x12,\n" +
	"\adevices\x18\x01 \x03(\v2\x12.device.DeviceInfoR\adevices\x129\n" +
	"\x0efailed_devices\x18\x02 \x03(\v2\x12.device.DeviceInfoR\rfailedDevices\"\xb1\x01\n" +
	"\x1cDeviceModifyCommParamRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x1a\n" +
	"\bposition\x18\x02 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"serial_num\x18\x03 \x01(\x05R\tserialNum\x12\x1f\n" +
	"\vcommon_type\x18\x04 \x01(\x05R\n" +
	"commonType\x12!\n" +
	"\fcommon_param\x18\x05 \x01(\tR\vcommonParam\"\x1f\n" +
	"\x1dDeviceModifyCommParamResponse\"\x88\x01\n" +
	"\x1bDeviceModifyProtocolRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x1a\n" +
	"\bposition\x18\x02 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"serial_num\x18\x03 \x01(\x05R\tserialNum\x12\x1a\n" +
	"\bprotocol\x18\x04 \x01(\tR\bprotocol\"\x1e\n" +
	"\x1cDeviceModifyProtocolResponse\"\x84\x01\n" +
	"\x14DeviceGetDataRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x1a\n" +
	"\bposition\x18\x02 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"serial_num\x18\x03 \x01(\x05R\tserialNum\x12\x1d\n" +
	"\n" +
	"param_type\x18\x04 \x03(\x05R\tparamType\"?\n" +
	"\x15DeviceGetDataResponse\x12&\n" +
	"\x04data\x18\x01 \x03(\v2\x12.device.DeviceDataR\x04data\"\x8d\x01\n" +
	"\x14DeviceSetDataRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x1a\n" +
	"\bposition\x18\x02 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"serial_num\x18\x03 \x01(\x05R\tserialNum\x12&\n" +
	"\x04data\x18\x04 \x03(\v2\x12.device.DeviceDataR\x04data\"\x17\n" +
	"\x15DeviceSetDataResponse\"v\n" +
	"\x11DeviceCommRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\x05R\x04type\x12\x1a\n" +
	"\bposition\x18\x02 \x01(\x05R\bposition\x12\x1d\n" +
	"\n" +
	"serial_num\x18\x03 \x01(\x05R\tserialNum\x12\x12\n" +
	"\x04data\x18\x04 \x01(\tR\x04data\"(\n" +
	"\x12DeviceCommResponse\x12\x12\n" +
	"\x04data\x18\x01 \x01(\tR\x04data2\xa7\x05\n" +
	"\x11BaseDeviceService\x12O\n" +
	"\x0eGetServiceInfo\x12\x1d.device.GetServiceInfoRequest\x1a\x1e.device.GetServiceInfoResponse\x12I\n" +
	"\x10DeviceInitialize\x12\x19.device.DeviceInitRequest\x1a\x1a.device.DeviceInitResponse\x12L\n" +
	"\rDeviceCleanup\x12\x1c.device.DeviceCleanupRequest\x1a\x1d.device.DeviceCleanupResponse\x12d\n" +
	"\x15DeviceModifyCommParam\x12$.device.DeviceModifyCommParamRequest\x1a%.device.DeviceModifyCommParamResponse\x12a\n" +
	"\x14DeviceModifyProtocol\x12#.device.DeviceModifyProtocolRequest\x1a$.device.DeviceModifyProtocolResponse\x12L\n" +
	"\rDeviceGetData\x12\x1c.device.DeviceGetDataRequest\x1a\x1d.device.DeviceGetDataResponse\x12L\n" +
	"\rDeviceSetData\x12\x1c.device.DeviceSetDataRequest\x1a\x1d.device.DeviceSetDataResponse\x12C\n" +
	"\n" +
	"DeviceComm\x12\x19.device.DeviceCommRequest\x1a\x1a.device.DeviceCommResponseB\n" +
	"Z\b./deviceb\x06proto3"

var (
	file_api_device_device_proto_rawDescOnce sync.Once
	file_api_device_device_proto_rawDescData []byte
)

func file_api_device_device_proto_rawDescGZIP() []byte {
	file_api_device_device_proto_rawDescOnce.Do(func() {
		file_api_device_device_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_device_device_proto_rawDesc), len(file_api_device_device_proto_rawDesc)))
	})
	return file_api_device_device_proto_rawDescData
}

var file_api_device_device_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_device_device_proto_goTypes = []any{
	(*ServiceInfo)(nil),                   // 0: device.ServiceInfo
	(*DeviceInfo)(nil),                    // 1: device.DeviceInfo
	(*DeviceData)(nil),                    // 2: device.DeviceData
	(*GetServiceInfoRequest)(nil),         // 3: device.GetServiceInfoRequest
	(*GetServiceInfoResponse)(nil),        // 4: device.GetServiceInfoResponse
	(*DeviceInitRequest)(nil),             // 5: device.DeviceInitRequest
	(*DeviceInitResponse)(nil),            // 6: device.DeviceInitResponse
	(*DeviceCleanupRequest)(nil),          // 7: device.DeviceCleanupRequest
	(*DeviceCleanupResponse)(nil),         // 8: device.DeviceCleanupResponse
	(*DeviceModifyCommParamRequest)(nil),  // 9: device.DeviceModifyCommParamRequest
	(*DeviceModifyCommParamResponse)(nil), // 10: device.DeviceModifyCommParamResponse
	(*DeviceModifyProtocolRequest)(nil),   // 11: device.DeviceModifyProtocolRequest
	(*DeviceModifyProtocolResponse)(nil),  // 12: device.DeviceModifyProtocolResponse
	(*DeviceGetDataRequest)(nil),          // 13: device.DeviceGetDataRequest
	(*DeviceGetDataResponse)(nil),         // 14: device.DeviceGetDataResponse
	(*DeviceSetDataRequest)(nil),          // 15: device.DeviceSetDataRequest
	(*DeviceSetDataResponse)(nil),         // 16: device.DeviceSetDataResponse
	(*DeviceCommRequest)(nil),             // 17: device.DeviceCommRequest
	(*DeviceCommResponse)(nil),            // 18: device.DeviceCommResponse
}
var file_api_device_device_proto_depIdxs = []int32{
	0,  // 0: device.GetServiceInfoResponse.services:type_name -> device.ServiceInfo
	1,  // 1: device.DeviceInitRequest.devices:type_name -> device.DeviceInfo
	1,  // 2: device.DeviceInitResponse.devices:type_name -> device.DeviceInfo
	1,  // 3: device.DeviceInitResponse.failed_devices:type_name -> device.DeviceInfo
	1,  // 4: device.DeviceCleanupRequest.devices:type_name -> device.DeviceInfo
	1,  // 5: device.DeviceCleanupResponse.devices:type_name -> device.DeviceInfo
	1,  // 6: device.DeviceCleanupResponse.failed_devices:type_name -> device.DeviceInfo
	2,  // 7: device.DeviceGetDataResponse.data:type_name -> device.DeviceData
	2,  // 8: device.DeviceSetDataRequest.data:type_name -> device.DeviceData
	3,  // 9: device.BaseDeviceService.GetServiceInfo:input_type -> device.GetServiceInfoRequest
	5,  // 10: device.BaseDeviceService.DeviceInitialize:input_type -> device.DeviceInitRequest
	7,  // 11: device.BaseDeviceService.DeviceCleanup:input_type -> device.DeviceCleanupRequest
	9,  // 12: device.BaseDeviceService.DeviceModifyCommParam:input_type -> device.DeviceModifyCommParamRequest
	11, // 13: device.BaseDeviceService.DeviceModifyProtocol:input_type -> device.DeviceModifyProtocolRequest
	13, // 14: device.BaseDeviceService.DeviceGetData:input_type -> device.DeviceGetDataRequest
	15, // 15: device.BaseDeviceService.DeviceSetData:input_type -> device.DeviceSetDataRequest
	17, // 16: device.BaseDeviceService.DeviceComm:input_type -> device.DeviceCommRequest
	4,  // 17: device.BaseDeviceService.GetServiceInfo:output_type -> device.GetServiceInfoResponse
	6,  // 18: device.BaseDeviceService.DeviceInitialize:output_type -> device.DeviceInitResponse
	8,  // 19: device.BaseDeviceService.DeviceCleanup:output_type -> device.DeviceCleanupResponse
	10, // 20: device.BaseDeviceService.DeviceModifyCommParam:output_type -> device.DeviceModifyCommParamResponse
	12, // 21: device.BaseDeviceService.DeviceModifyProtocol:output_type -> device.DeviceModifyProtocolResponse
	14, // 22: device.BaseDeviceService.DeviceGetData:output_type -> device.DeviceGetDataResponse
	16, // 23: device.BaseDeviceService.DeviceSetData:output_type -> device.DeviceSetDataResponse
	18, // 24: device.BaseDeviceService.DeviceComm:output_type -> device.DeviceCommResponse
	17, // [17:25] is the sub-list for method output_type
	9,  // [9:17] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_api_device_device_proto_init() }
func file_api_device_device_proto_init() {
	if File_api_device_device_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_device_device_proto_rawDesc), len(file_api_device_device_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_device_device_proto_goTypes,
		DependencyIndexes: file_api_device_device_proto_depIdxs,
		MessageInfos:      file_api_device_device_proto_msgTypes,
	}.Build()
	File_api_device_device_proto = out.File
	file_api_device_device_proto_goTypes = nil
	file_api_device_device_proto_depIdxs = nil
}
