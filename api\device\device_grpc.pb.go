// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/device/device.proto

package device

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BaseDeviceService_GetServiceInfo_FullMethodName        = "/device.BaseDeviceService/GetServiceInfo"
	BaseDeviceService_DeviceInitialize_FullMethodName      = "/device.BaseDeviceService/DeviceInitialize"
	BaseDeviceService_DeviceCleanup_FullMethodName         = "/device.BaseDeviceService/DeviceCleanup"
	BaseDeviceService_DeviceModifyCommParam_FullMethodName = "/device.BaseDeviceService/DeviceModifyCommParam"
	BaseDeviceService_DeviceModifyProtocol_FullMethodName  = "/device.BaseDeviceService/DeviceModifyProtocol"
	BaseDeviceService_DeviceGetData_FullMethodName         = "/device.BaseDeviceService/DeviceGetData"
	BaseDeviceService_DeviceSetData_FullMethodName         = "/device.BaseDeviceService/DeviceSetData"
	BaseDeviceService_DeviceComm_FullMethodName            = "/device.BaseDeviceService/DeviceComm"
)

// BaseDeviceServiceClient is the client API for BaseDeviceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 基础设备服务定义（访问除服务信息外的接口，皆需要在 metadata 中携带初始化 detection 服务返回的 token -> key-authorization, value-token）
type BaseDeviceServiceClient interface {
	GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error)
	DeviceInitialize(ctx context.Context, in *DeviceInitRequest, opts ...grpc.CallOption) (*DeviceInitResponse, error)
	DeviceCleanup(ctx context.Context, in *DeviceCleanupRequest, opts ...grpc.CallOption) (*DeviceCleanupResponse, error)
	DeviceModifyCommParam(ctx context.Context, in *DeviceModifyCommParamRequest, opts ...grpc.CallOption) (*DeviceModifyCommParamResponse, error)
	DeviceModifyProtocol(ctx context.Context, in *DeviceModifyProtocolRequest, opts ...grpc.CallOption) (*DeviceModifyProtocolResponse, error)
	DeviceGetData(ctx context.Context, in *DeviceGetDataRequest, opts ...grpc.CallOption) (*DeviceGetDataResponse, error)
	DeviceSetData(ctx context.Context, in *DeviceSetDataRequest, opts ...grpc.CallOption) (*DeviceSetDataResponse, error)
	DeviceComm(ctx context.Context, in *DeviceCommRequest, opts ...grpc.CallOption) (*DeviceCommResponse, error)
}

type baseDeviceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBaseDeviceServiceClient(cc grpc.ClientConnInterface) BaseDeviceServiceClient {
	return &baseDeviceServiceClient{cc}
}

func (c *baseDeviceServiceClient) GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceInfoResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_GetServiceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDeviceServiceClient) DeviceInitialize(ctx context.Context, in *DeviceInitRequest, opts ...grpc.CallOption) (*DeviceInitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceInitResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_DeviceInitialize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDeviceServiceClient) DeviceCleanup(ctx context.Context, in *DeviceCleanupRequest, opts ...grpc.CallOption) (*DeviceCleanupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceCleanupResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_DeviceCleanup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDeviceServiceClient) DeviceModifyCommParam(ctx context.Context, in *DeviceModifyCommParamRequest, opts ...grpc.CallOption) (*DeviceModifyCommParamResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceModifyCommParamResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_DeviceModifyCommParam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDeviceServiceClient) DeviceModifyProtocol(ctx context.Context, in *DeviceModifyProtocolRequest, opts ...grpc.CallOption) (*DeviceModifyProtocolResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceModifyProtocolResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_DeviceModifyProtocol_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDeviceServiceClient) DeviceGetData(ctx context.Context, in *DeviceGetDataRequest, opts ...grpc.CallOption) (*DeviceGetDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceGetDataResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_DeviceGetData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDeviceServiceClient) DeviceSetData(ctx context.Context, in *DeviceSetDataRequest, opts ...grpc.CallOption) (*DeviceSetDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceSetDataResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_DeviceSetData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *baseDeviceServiceClient) DeviceComm(ctx context.Context, in *DeviceCommRequest, opts ...grpc.CallOption) (*DeviceCommResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceCommResponse)
	err := c.cc.Invoke(ctx, BaseDeviceService_DeviceComm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BaseDeviceServiceServer is the server API for BaseDeviceService service.
// All implementations must embed UnimplementedBaseDeviceServiceServer
// for forward compatibility.
//
// 基础设备服务定义（访问除服务信息外的接口，皆需要在 metadata 中携带初始化 detection 服务返回的 token -> key-authorization, value-token）
type BaseDeviceServiceServer interface {
	GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error)
	DeviceInitialize(context.Context, *DeviceInitRequest) (*DeviceInitResponse, error)
	DeviceCleanup(context.Context, *DeviceCleanupRequest) (*DeviceCleanupResponse, error)
	DeviceModifyCommParam(context.Context, *DeviceModifyCommParamRequest) (*DeviceModifyCommParamResponse, error)
	DeviceModifyProtocol(context.Context, *DeviceModifyProtocolRequest) (*DeviceModifyProtocolResponse, error)
	DeviceGetData(context.Context, *DeviceGetDataRequest) (*DeviceGetDataResponse, error)
	DeviceSetData(context.Context, *DeviceSetDataRequest) (*DeviceSetDataResponse, error)
	DeviceComm(context.Context, *DeviceCommRequest) (*DeviceCommResponse, error)
	mustEmbedUnimplementedBaseDeviceServiceServer()
}

// UnimplementedBaseDeviceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBaseDeviceServiceServer struct{}

func (UnimplementedBaseDeviceServiceServer) GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceInfo not implemented")
}
func (UnimplementedBaseDeviceServiceServer) DeviceInitialize(context.Context, *DeviceInitRequest) (*DeviceInitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceInitialize not implemented")
}
func (UnimplementedBaseDeviceServiceServer) DeviceCleanup(context.Context, *DeviceCleanupRequest) (*DeviceCleanupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceCleanup not implemented")
}
func (UnimplementedBaseDeviceServiceServer) DeviceModifyCommParam(context.Context, *DeviceModifyCommParamRequest) (*DeviceModifyCommParamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceModifyCommParam not implemented")
}
func (UnimplementedBaseDeviceServiceServer) DeviceModifyProtocol(context.Context, *DeviceModifyProtocolRequest) (*DeviceModifyProtocolResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceModifyProtocol not implemented")
}
func (UnimplementedBaseDeviceServiceServer) DeviceGetData(context.Context, *DeviceGetDataRequest) (*DeviceGetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceGetData not implemented")
}
func (UnimplementedBaseDeviceServiceServer) DeviceSetData(context.Context, *DeviceSetDataRequest) (*DeviceSetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceSetData not implemented")
}
func (UnimplementedBaseDeviceServiceServer) DeviceComm(context.Context, *DeviceCommRequest) (*DeviceCommResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceComm not implemented")
}
func (UnimplementedBaseDeviceServiceServer) mustEmbedUnimplementedBaseDeviceServiceServer() {}
func (UnimplementedBaseDeviceServiceServer) testEmbeddedByValue()                           {}

// UnsafeBaseDeviceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BaseDeviceServiceServer will
// result in compilation errors.
type UnsafeBaseDeviceServiceServer interface {
	mustEmbedUnimplementedBaseDeviceServiceServer()
}

func RegisterBaseDeviceServiceServer(s grpc.ServiceRegistrar, srv BaseDeviceServiceServer) {
	// If the following call pancis, it indicates UnimplementedBaseDeviceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BaseDeviceService_ServiceDesc, srv)
}

func _BaseDeviceService_GetServiceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).GetServiceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_GetServiceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).GetServiceInfo(ctx, req.(*GetServiceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDeviceService_DeviceInitialize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceInitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).DeviceInitialize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_DeviceInitialize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).DeviceInitialize(ctx, req.(*DeviceInitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDeviceService_DeviceCleanup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceCleanupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).DeviceCleanup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_DeviceCleanup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).DeviceCleanup(ctx, req.(*DeviceCleanupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDeviceService_DeviceModifyCommParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceModifyCommParamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).DeviceModifyCommParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_DeviceModifyCommParam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).DeviceModifyCommParam(ctx, req.(*DeviceModifyCommParamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDeviceService_DeviceModifyProtocol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceModifyProtocolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).DeviceModifyProtocol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_DeviceModifyProtocol_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).DeviceModifyProtocol(ctx, req.(*DeviceModifyProtocolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDeviceService_DeviceGetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceGetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).DeviceGetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_DeviceGetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).DeviceGetData(ctx, req.(*DeviceGetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDeviceService_DeviceSetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceSetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).DeviceSetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_DeviceSetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).DeviceSetData(ctx, req.(*DeviceSetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BaseDeviceService_DeviceComm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceCommRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BaseDeviceServiceServer).DeviceComm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BaseDeviceService_DeviceComm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BaseDeviceServiceServer).DeviceComm(ctx, req.(*DeviceCommRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BaseDeviceService_ServiceDesc is the grpc.ServiceDesc for BaseDeviceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BaseDeviceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "device.BaseDeviceService",
	HandlerType: (*BaseDeviceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServiceInfo",
			Handler:    _BaseDeviceService_GetServiceInfo_Handler,
		},
		{
			MethodName: "DeviceInitialize",
			Handler:    _BaseDeviceService_DeviceInitialize_Handler,
		},
		{
			MethodName: "DeviceCleanup",
			Handler:    _BaseDeviceService_DeviceCleanup_Handler,
		},
		{
			MethodName: "DeviceModifyCommParam",
			Handler:    _BaseDeviceService_DeviceModifyCommParam_Handler,
		},
		{
			MethodName: "DeviceModifyProtocol",
			Handler:    _BaseDeviceService_DeviceModifyProtocol_Handler,
		},
		{
			MethodName: "DeviceGetData",
			Handler:    _BaseDeviceService_DeviceGetData_Handler,
		},
		{
			MethodName: "DeviceSetData",
			Handler:    _BaseDeviceService_DeviceSetData_Handler,
		},
		{
			MethodName: "DeviceComm",
			Handler:    _BaseDeviceService_DeviceComm_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/device/device.proto",
}
