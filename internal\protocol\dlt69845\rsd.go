package dlt698

import (
	"bytes"
)

// RSD 记录选择描述符类型
type RSDType byte

const (
	RSDTypeEqual         RSDType = 1  // 等于
	RSDTypeGreater       RSDType = 2  // 大于
	RSDTypeGreaterEqual  RSDType = 3  // 大于等于
	RSDTypeLess          RSDType = 4  // 小于
	RSDTypeLessEqual     RSDType = 5  // 小于等于
	RSDTypeNotEqual      RSDType = 6  // 不等于
	RSDTypeSection       RSDType = 7  // 区间
	RSDTypeGreaterBefore RSDType = 8  // 大于等于，小于
	RSDTypeGreaterBegin  RSDType = 9  // 大于等于，小于等于
	RSDTypeLessBefore    RSDType = 10 // 大于，小于
	RSDTypeLessBegin     RSDType = 11 // 大于，小于等于
)

// RSDInterface 记录选择描述符接口
type RSDInterface interface {
	Type() RSDType
	Encode() []byte
}

// RSD_OAD 对象属性描述符类型的RSD
type RSD_OAD struct {
	OAD   *OAD
	Value []byte
}

func (r *RSD_OAD) Type() RSDType {
	return RSDTypeEqual
}

func (r *RSD_OAD) Encode() []byte {
	buf := new(bytes.Buffer)
	buf.WriteByte(byte(r.Type()))
	buf.Write(r.OAD.Encode())
	buf.Write(r.Value)
	return buf.Bytes()
}

// RSD_Range 区间类型的RSD
type RSD_Range struct {
	OAD        *OAD
	RangeType  RSDType
	ValueStart []byte
	ValueEnd   []byte
}

func (r *RSD_Range) Type() RSDType {
	return r.RangeType
}

func (r *RSD_Range) Encode() []byte {
	buf := new(bytes.Buffer)
	buf.WriteByte(byte(r.Type()))
	buf.Write(r.OAD.Encode())
	buf.Write(r.ValueStart)
	buf.Write(r.ValueEnd)
	return buf.Bytes()
}

// NewRSD_Equal 创建等于类型的RSD
func NewRSD_Equal(oad *OAD, value interface{}) (*RSD_OAD, error) {
	encodedValue, err := encodeValue(value)
	if err != nil {
		return nil, err
	}

	return &RSD_OAD{
		OAD:   oad,
		Value: encodedValue,
	}, nil
}

// NewRSD_Range 创建区间类型的RSD
func NewRSD_Range(oad *OAD, rangeType RSDType, valueStart, valueEnd interface{}) (*RSD_Range, error) {
	encodedStart, err := encodeValue(valueStart)
	if err != nil {
		return nil, err
	}

	encodedEnd, err := encodeValue(valueEnd)
	if err != nil {
		return nil, err
	}

	return &RSD_Range{
		OAD:        oad,
		RangeType:  rangeType,
		ValueStart: encodedStart,
		ValueEnd:   encodedEnd,
	}, nil
}

// RSD_NULL 空的RSD
type RSD_NULL struct{}

func (r *RSD_NULL) Type() RSDType {
	return 0
}

func (r *RSD_NULL) Encode() []byte {
	return []byte{0}
}

// NewRSD_NULL 创建空的RSD
func NewRSD_NULL() *RSD_NULL {
	return &RSD_NULL{}
}
