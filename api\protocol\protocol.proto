syntax = "proto3";

package protocol;

option go_package = "./protocol";

// 基础协议服务定义
service BaseProtocolService {
    rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
    rpc ProtocolOrganization(CPORequest) returns (CPOResponse);
    rpc ProtocolParsing(CPPRequest) returns (CPPResponse);
}

// 服务信息
message ServiceInfo {
    string name = 1;        // 服务名称
    string version = 2;     // 服务版本
    string description = 3; // 服务描述
}

// 服务信息获取请求
message GetServiceInfoRequest {
    string name = 1;    // 协议服务名称，如果为空("")则获取所有服务信息
}

// 服务信息获取响应
message GetServiceInfoResponse {
    repeated ServiceInfo services = 1;
}

// 通讯协议组织请求 Communication Protocol Organization
message CPORequest {
    int32 type = 1;     // 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
    string params = 2;  // 组织参数, JSON 格式（待补充完善，根据具体协议类型，例 Modbus 需要寄存器类型、地址、数据等）
}

// 协议组织响应
message CPOResponse {
    string frame = 1;    // 组织后的数据, 字符串格式
}

// 通讯协议解析请求 Communication Protocol Parsing
message CPPRequest {
    int32 type = 1;     // 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2
    string frame = 2;   // 待解析的数据, 字符串格式
}

// 协议解析响应
message CPPResponse {
    string data = 1;    // 解析后的数据, JSON 格式（待补充，根据具体协议类型）
}

